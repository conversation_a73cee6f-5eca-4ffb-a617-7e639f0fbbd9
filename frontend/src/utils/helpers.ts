export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('vi-VN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
};

export const getFileIcon = (mimeType: string): string => {
  // Images
  if (mimeType.startsWith('image/')) {
    if (mimeType.includes('svg')) return '🎨';
    if (mimeType.includes('gif')) return '🎞️';
    return '🖼️';
  }

  // Videos
  if (mimeType.startsWith('video/')) {
    return '🎥';
  }

  // Audio
  if (mimeType.startsWith('audio/')) {
    if (mimeType.includes('mp3') || mimeType.includes('mpeg')) return '🎵';
    if (mimeType.includes('wav')) return '🔊';
    return '🎶';
  }

  // Documents
  if (mimeType.includes('pdf')) return '📄';
  if (mimeType.includes('document') || mimeType.includes('word')) return '📝';
  if (mimeType.includes('spreadsheet') || mimeType.includes('excel')) return '📊';
  if (mimeType.includes('presentation') || mimeType.includes('powerpoint')) return '📽️';

  // Code files
  if (mimeType.includes('javascript')) return '📜';
  if (mimeType.includes('json')) return '📋';
  if (mimeType.includes('css')) return '🎨';
  if (mimeType.includes('html')) return '🌐';
  if (mimeType.includes('xml')) return '📰';

  // Text files
  if (mimeType.startsWith('text/')) {
    if (mimeType.includes('csv')) return '📊';
    if (mimeType.includes('markdown')) return '📝';
    return '📃';
  }

  // Archives
  if (mimeType.includes('zip')) return '📦';
  if (mimeType.includes('rar')) return '📦';
  if (mimeType.includes('tar') || mimeType.includes('gzip')) return '🗜️';
  if (mimeType.includes('7z')) return '📦';

  // Executables
  if (mimeType.includes('executable') || mimeType.includes('application/x-')) return '⚙️';

  // Default
  return '📄';
};

export const isImageFile = (mimeType: string): boolean => {
  return mimeType.startsWith('image/');
};

export const isVideoFile = (mimeType: string): boolean => {
  return mimeType.startsWith('video/');
};

export const isAudioFile = (mimeType: string): boolean => {
  return mimeType.startsWith('audio/');
};

export const isPdfFile = (mimeType: string): boolean => {
  return mimeType === 'application/pdf';
};

export const isTextFile = (mimeType: string): boolean => {
  return mimeType.startsWith('text/') ||
         mimeType.includes('json') ||
         mimeType.includes('javascript') ||
         mimeType.includes('css') ||
         mimeType.includes('xml') ||
         mimeType.includes('yaml') ||
         mimeType.includes('yml');
};

export const isOfficeFile = (mimeType: string): boolean => {
  return mimeType.includes('document') ||
         mimeType.includes('word') ||
         mimeType.includes('spreadsheet') ||
         mimeType.includes('excel') ||
         mimeType.includes('presentation') ||
         mimeType.includes('powerpoint') ||
         mimeType.includes('officedocument');
};

export const isArchiveFile = (mimeType: string): boolean => {
  return mimeType.includes('zip') ||
         mimeType.includes('rar') ||
         mimeType.includes('tar') ||
         mimeType.includes('gzip') ||
         mimeType.includes('7z') ||
         mimeType.includes('archive');
};

export const getFileCategory = (mimeType: string): string => {
  if (isImageFile(mimeType)) return 'image';
  if (isVideoFile(mimeType)) return 'video';
  if (isAudioFile(mimeType)) return 'audio';
  if (isPdfFile(mimeType)) return 'pdf';
  if (isTextFile(mimeType)) return 'text';
  if (isOfficeFile(mimeType)) return 'office';
  if (isArchiveFile(mimeType)) return 'archive';
  return 'other';
};

export const truncateFileName = (fileName: string, maxLength: number = 30): string => {
  if (fileName.length <= maxLength) return fileName;

  const extension = fileName.split('.').pop();
  const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.'));

  if (extension) {
    const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...';
    return `${truncatedName}.${extension}`;
  }

  return fileName.substring(0, maxLength - 3) + '...';
};

export const validateFileName = (fileName: string): boolean => {
  const invalidChars = /[<>:"/\\|?*]/;
  return !invalidChars.test(fileName) && fileName.trim().length > 0;
};

// Detect if user is on iOS/mobile device
export const isIOSDevice = (): boolean => {
  return /iPad|iPhone|iPod/.test(navigator.userAgent) ||
         (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1);
};

export const isMobileDevice = (): boolean => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

// Get actual file size using ArrayBuffer (more accurate on mobile)
export const getActualFileSize = (file: File): Promise<number> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const arrayBuffer = e.target?.result as ArrayBuffer;
      if (arrayBuffer) {
        resolve(arrayBuffer.byteLength);
      } else {
        resolve(file.size); // fallback to File.size
      }
    };
    reader.onerror = () => {
      resolve(file.size); // fallback to File.size
    };
    reader.readAsArrayBuffer(file);
  });
};

// Format file size with mobile device warning
export const formatFileSizeWithWarning = (file: File): string => {
  const basicSize = formatFileSize(file.size);

  if (isIOSDevice() || isMobileDevice()) {
    return `${basicSize} ⚠️`;
  }

  return basicSize;
};

export const downloadFile = (url: string, fileName: string): void => {
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
